{"inputs": ["/opt/homebrew/Caskroom/flutter/3.32.5/flutter/bin/cache/engine.stamp"], "outputs": ["/Users/<USER>/code/personal_code/flutter-prac/flashcard_app/.dart_tool/flutter_build/1ccc8e18196ba3283604a67b4b9405c1/flutter.js", "/Users/<USER>/code/personal_code/flutter-prac/flashcard_app/.dart_tool/flutter_build/1ccc8e18196ba3283604a67b4b9405c1/canvaskit/skwasm.js", "/Users/<USER>/code/personal_code/flutter-prac/flashcard_app/.dart_tool/flutter_build/1ccc8e18196ba3283604a67b4b9405c1/canvaskit/skwasm.js.symbols", "/Users/<USER>/code/personal_code/flutter-prac/flashcard_app/.dart_tool/flutter_build/1ccc8e18196ba3283604a67b4b9405c1/canvaskit/canvaskit.js.symbols", "/Users/<USER>/code/personal_code/flutter-prac/flashcard_app/.dart_tool/flutter_build/1ccc8e18196ba3283604a67b4b9405c1/canvaskit/skwasm.wasm", "/Users/<USER>/code/personal_code/flutter-prac/flashcard_app/.dart_tool/flutter_build/1ccc8e18196ba3283604a67b4b9405c1/canvaskit/chromium/canvaskit.js.symbols", "/Users/<USER>/code/personal_code/flutter-prac/flashcard_app/.dart_tool/flutter_build/1ccc8e18196ba3283604a67b4b9405c1/canvaskit/chromium/canvaskit.js", "/Users/<USER>/code/personal_code/flutter-prac/flashcard_app/.dart_tool/flutter_build/1ccc8e18196ba3283604a67b4b9405c1/canvaskit/chromium/canvaskit.wasm", "/Users/<USER>/code/personal_code/flutter-prac/flashcard_app/.dart_tool/flutter_build/1ccc8e18196ba3283604a67b4b9405c1/canvaskit/canvaskit.js", "/Users/<USER>/code/personal_code/flutter-prac/flashcard_app/.dart_tool/flutter_build/1ccc8e18196ba3283604a67b4b9405c1/canvaskit/canvaskit.wasm"]}