// 單字卡應用程式的基本測試
//
// 測試應用程式的基本功能和 Widget 行為

import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';

import 'package:flashcard_app/main.dart';

void main() {
  testWidgets('Flashcard app smoke test', (WidgetTester tester) async {
    // 建立應用程式並觸發一個 frame
    await tester.pumpWidget(const FlashcardApp());

    // 等待載入完成
    await tester.pumpAndSettle();

    // 驗證應用程式標題是否正確顯示
    expect(find.text('單字卡學習'), findsOneWidget);

    // 驗證是否有新增按鈕
    expect(find.byIcon(Icons.add), findsOneWidget);

    // 驗證分頁標籤
    expect(find.text('全部'), findsOneWidget);
    expect(find.text('未學習'), findsOneWidget);
  });
}
