import 'package:flutter/material.dart';

/// 應用程式常數定義
class AppConstants {
  // 應用程式資訊
  static const String appName = '單字卡學習';
  static const String appVersion = '1.0.0';

  // 顏色主題
  static const Color primaryColor = Color(0xFF2196F3);
  static const Color secondaryColor = Color(0xFF03DAC6);
  static const Color errorColor = Color(0xFFB00020);
  static const Color successColor = Color(0xFF4CAF50);
  static const Color warningColor = Color(0xFFFF9800);

  // 文字樣式
  static const TextStyle titleTextStyle = TextStyle(
    fontSize: 24,
    fontWeight: FontWeight.bold,
    color: Colors.black87,
  );

  static const TextStyle subtitleTextStyle = TextStyle(
    fontSize: 18,
    fontWeight: FontWeight.w500,
    color: Colors.black54,
  );

  static const TextStyle bodyTextStyle = TextStyle(
    fontSize: 16,
    color: Colors.black87,
  );

  static const TextStyle captionTextStyle = TextStyle(
    fontSize: 14,
    color: Colors.black54,
  );

  // 間距
  static const double smallPadding = 8.0;
  static const double mediumPadding = 16.0;
  static const double largePadding = 24.0;
  static const double extraLargePadding = 32.0;

  // 圓角
  static const double smallRadius = 4.0;
  static const double mediumRadius = 8.0;
  static const double largeRadius = 16.0;

  // 動畫持續時間
  static const Duration shortAnimationDuration = Duration(milliseconds: 200);
  static const Duration mediumAnimationDuration = Duration(milliseconds: 300);
  static const Duration longAnimationDuration = Duration(milliseconds: 500);

  // 卡片尺寸
  static const double cardMinHeight = 120.0;
  static const double cardMaxHeight = 200.0;
  static const double cardAspectRatio = 16 / 9;

  // 訊息文字
  static const String emptyFlashcardsMessage = '還沒有任何單字卡\n點擊右下角的按鈕來新增第一張卡片！';
  static const String noUnlearnedCardsMessage = '恭喜！你已經學完所有單字卡了！';
  static const String deleteConfirmMessage = '確定要刪除這張單字卡嗎？';
  static const String saveSuccessMessage = '儲存成功！';
  static const String deleteSuccessMessage = '刪除成功！';
  static const String errorMessage = '發生錯誤，請稍後再試';

  // 表單驗證
  static const int maxContentLength = 500;
  static const String frontEmptyError = '正面內容不能為空';
  static const String backEmptyError = '背面內容不能為空';
  static const String contentTooLongError = '內容不能超過 500 個字元';
}
