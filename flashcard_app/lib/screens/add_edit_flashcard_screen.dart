import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

import '../models/flashcard.dart';
import '../providers/flashcard_provider.dart';
import '../widgets/custom_button.dart';
import '../utils/constants.dart';

/// 新增/編輯單字卡畫面
/// 
/// 提供表單介面讓使用者新增或編輯單字卡內容
class AddEditFlashcardScreen extends StatefulWidget {
  final Flashcard? flashcard;

  const AddEditFlashcardScreen({
    super.key,
    this.flashcard,
  });

  @override
  State<AddEditFlashcardScreen> createState() => _AddEditFlashcardScreenState();
}

class _AddEditFlashcardScreenState extends State<AddEditFlashcardScreen> {
  final _formKey = GlobalKey<FormState>();
  final _frontController = TextEditingController();
  final _backController = TextEditingController();
  final _frontFocusNode = FocusNode();
  final _backFocusNode = FocusNode();
  
  bool _isLoading = false;
  bool get _isEditing => widget.flashcard != null;

  @override
  void initState() {
    super.initState();
    if (_isEditing) {
      _frontController.text = widget.flashcard!.front;
      _backController.text = widget.flashcard!.back;
    }
  }

  @override
  void dispose() {
    _frontController.dispose();
    _backController.dispose();
    _frontFocusNode.dispose();
    _backFocusNode.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(
          _isEditing ? '編輯單字卡' : '新增單字卡',
          style: AppConstants.titleTextStyle.copyWith(color: Colors.white),
        ),
        backgroundColor: AppConstants.primaryColor,
        foregroundColor: Colors.white,
        elevation: 0,
      ),
      body: Consumer<FlashcardProvider>(
        builder: (context, provider, child) {
          return SingleChildScrollView(
            padding: const EdgeInsets.all(AppConstants.mediumPadding),
            child: Form(
              key: _formKey,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.stretch,
                children: [
                  // 說明文字
                  Container(
                    padding: const EdgeInsets.all(AppConstants.mediumPadding),
                    decoration: BoxDecoration(
                      color: AppConstants.primaryColor.withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(AppConstants.mediumRadius),
                    ),
                    child: Row(
                      children: [
                        Icon(
                          Icons.info_outline,
                          color: AppConstants.primaryColor,
                        ),
                        const SizedBox(width: AppConstants.smallPadding),
                        Expanded(
                          child: Text(
                            _isEditing
                                ? '修改單字卡的正面和背面內容'
                                : '建立一張新的單字卡，填入正面和背面的內容',
                            style: AppConstants.captionTextStyle.copyWith(
                              color: AppConstants.primaryColor,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                  
                  const SizedBox(height: AppConstants.largePadding),
                  
                  // 正面內容輸入
                  _buildInputField(
                    controller: _frontController,
                    focusNode: _frontFocusNode,
                    label: '正面內容',
                    hint: '輸入單字、問題或提示...',
                    icon: Icons.quiz,
                    validator: (value) {
                      if (value == null || value.trim().isEmpty) {
                        return AppConstants.frontEmptyError;
                      }
                      if (value.trim().length > AppConstants.maxContentLength) {
                        return AppConstants.contentTooLongError;
                      }
                      return null;
                    },
                    onFieldSubmitted: (_) {
                      _backFocusNode.requestFocus();
                    },
                  ),
                  
                  const SizedBox(height: AppConstants.mediumPadding),
                  
                  // 背面內容輸入
                  _buildInputField(
                    controller: _backController,
                    focusNode: _backFocusNode,
                    label: '背面內容',
                    hint: '輸入答案、解釋或翻譯...',
                    icon: Icons.lightbulb_outline,
                    validator: (value) {
                      if (value == null || value.trim().isEmpty) {
                        return AppConstants.backEmptyError;
                      }
                      if (value.trim().length > AppConstants.maxContentLength) {
                        return AppConstants.contentTooLongError;
                      }
                      return null;
                    },
                    onFieldSubmitted: (_) {
                      _saveFlashcard();
                    },
                  ),
                  
                  const SizedBox(height: AppConstants.largePadding),
                  
                  // 錯誤訊息顯示
                  if (provider.errorMessage != null) ...[
                    Container(
                      padding: const EdgeInsets.all(AppConstants.mediumPadding),
                      decoration: BoxDecoration(
                        color: AppConstants.errorColor.withValues(alpha: 0.1),
                        borderRadius: BorderRadius.circular(AppConstants.mediumRadius),
                        border: Border.all(
                          color: AppConstants.errorColor.withValues(alpha: 0.3),
                        ),
                      ),
                      child: Row(
                        children: [
                          Icon(
                            Icons.error_outline,
                            color: AppConstants.errorColor,
                          ),
                          const SizedBox(width: AppConstants.smallPadding),
                          Expanded(
                            child: Text(
                              provider.errorMessage!,
                              style: TextStyle(
                                color: AppConstants.errorColor,
                                fontSize: 14,
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                    const SizedBox(height: AppConstants.mediumPadding),
                  ],
                  
                  // 操作按鈕
                  Row(
                    children: [
                      Expanded(
                        child: CustomButton(
                          text: '取消',
                          onPressed: _isLoading ? null : () => Navigator.of(context).pop(),
                          isOutlined: true,
                        ),
                      ),
                      const SizedBox(width: AppConstants.mediumPadding),
                      Expanded(
                        child: CustomButton(
                          text: _isEditing ? '更新' : '儲存',
                          onPressed: _isLoading ? null : _saveFlashcard,
                          isLoading: _isLoading,
                          icon: _isEditing ? Icons.update : Icons.save,
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          );
        },
      ),
    );
  }

  Widget _buildInputField({
    required TextEditingController controller,
    required FocusNode focusNode,
    required String label,
    required String hint,
    required IconData icon,
    required String? Function(String?) validator,
    void Function(String)? onFieldSubmitted,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: AppConstants.subtitleTextStyle.copyWith(
            fontSize: 16,
            fontWeight: FontWeight.w600,
          ),
        ),
        const SizedBox(height: AppConstants.smallPadding),
        TextFormField(
          controller: controller,
          focusNode: focusNode,
          validator: validator,
          onFieldSubmitted: onFieldSubmitted,
          maxLines: 3,
          maxLength: AppConstants.maxContentLength,
          decoration: InputDecoration(
            hintText: hint,
            prefixIcon: Icon(icon, color: AppConstants.primaryColor),
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(AppConstants.mediumRadius),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(AppConstants.mediumRadius),
              borderSide: BorderSide(
                color: AppConstants.primaryColor,
                width: 2,
              ),
            ),
            errorBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(AppConstants.mediumRadius),
              borderSide: BorderSide(
                color: AppConstants.errorColor,
                width: 2,
              ),
            ),
            filled: true,
            fillColor: Colors.grey[50],
          ),
        ),
      ],
    );
  }

  Future<void> _saveFlashcard() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    setState(() {
      _isLoading = true;
    });

    final provider = context.read<FlashcardProvider>();
    provider.clearError();

    bool success;
    if (_isEditing) {
      success = await provider.updateFlashcard(
        id: widget.flashcard!.id!,
        front: _frontController.text,
        back: _backController.text,
      );
    } else {
      success = await provider.addFlashcard(
        front: _frontController.text,
        back: _backController.text,
      );
    }

    setState(() {
      _isLoading = false;
    });

    if (success) {
      if (mounted) {
        Navigator.of(context).pop();
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(_isEditing ? '更新成功！' : AppConstants.saveSuccessMessage),
            backgroundColor: AppConstants.successColor,
          ),
        );
      }
    }
  }
}
