import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:flutter_staggered_animations/flutter_staggered_animations.dart';

import '../providers/flashcard_provider.dart';
import '../widgets/flashcard_widget.dart';
import '../widgets/custom_button.dart';
import '../utils/constants.dart';
import 'add_edit_flashcard_screen.dart';
import 'study_screen.dart';

/// 主畫面
/// 
/// 顯示所有單字卡的列表，提供新增、編輯、刪除等功能
class HomeScreen extends StatefulWidget {
  const HomeScreen({super.key});

  @override
  State<HomeScreen> createState() => _HomeScreenState();
}

class _HomeScreenState extends State<HomeScreen> with TickerProviderStateMixin {
  late TabController _tabController;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
    
    // 載入單字卡資料
    WidgetsBinding.instance.addPostFrameCallback((_) {
      context.read<FlashcardProvider>().loadFlashcards();
    });
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text(
          AppConstants.appName,
          style: AppConstants.titleTextStyle,
        ),
        backgroundColor: AppConstants.primaryColor,
        foregroundColor: Colors.white,
        elevation: 0,
        bottom: TabBar(
          controller: _tabController,
          indicatorColor: Colors.white,
          labelColor: Colors.white,
          unselectedLabelColor: Colors.white70,
          tabs: const [
            Tab(text: '全部', icon: Icon(Icons.list)),
            Tab(text: '未學習', icon: Icon(Icons.school)),
            Tab(text: '已學習', icon: Icon(Icons.check_circle)),
          ],
        ),
      ),
      body: Consumer<FlashcardProvider>(
        builder: (context, provider, child) {
          if (provider.isLoading) {
            return const Center(
              child: CircularProgressIndicator(),
            );
          }

          if (provider.errorMessage != null) {
            return Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(
                    Icons.error_outline,
                    size: 64,
                    color: AppConstants.errorColor,
                  ),
                  const SizedBox(height: AppConstants.mediumPadding),
                  Text(
                    provider.errorMessage!,
                    style: AppConstants.bodyTextStyle,
                    textAlign: TextAlign.center,
                  ),
                  const SizedBox(height: AppConstants.mediumPadding),
                  CustomButton(
                    text: '重試',
                    onPressed: () {
                      provider.clearError();
                      provider.loadFlashcards();
                    },
                  ),
                ],
              ),
            );
          }

          return Column(
            children: [
              // 統計資訊
              _buildStatsCard(provider),
              
              // 分頁內容
              Expanded(
                child: TabBarView(
                  controller: _tabController,
                  children: [
                    _buildFlashcardList(provider.flashcards, '還沒有任何單字卡'),
                    _buildFlashcardList(provider.unlearnedFlashcards, '沒有未學習的單字卡'),
                    _buildFlashcardList(provider.learnedFlashcards, '沒有已學習的單字卡'),
                  ],
                ),
              ),
            ],
          );
        },
      ),
      floatingActionButton: Column(
        mainAxisAlignment: MainAxisAlignment.end,
        children: [
          // 學習模式按鈕
          Consumer<FlashcardProvider>(
            builder: (context, provider, child) {
              final hasUnlearnedCards = provider.unlearnedFlashcards.isNotEmpty;
              return CustomFloatingActionButton(
                onPressed: hasUnlearnedCards
                    ? () => _navigateToStudyScreen(context)
                    : null,
                icon: Icons.school,
                tooltip: '開始學習',
                backgroundColor: hasUnlearnedCards
                    ? AppConstants.successColor
                    : Colors.grey,
                mini: true,
              );
            },
          ),
          const SizedBox(height: AppConstants.smallPadding),
          
          // 新增按鈕
          CustomFloatingActionButton(
            onPressed: () => _navigateToAddScreen(context),
            icon: Icons.add,
            tooltip: '新增單字卡',
          ),
        ],
      ),
    );
  }

  Widget _buildStatsCard(FlashcardProvider provider) {
    final stats = provider.studyStats;
    return Container(
      margin: const EdgeInsets.all(AppConstants.mediumPadding),
      padding: const EdgeInsets.all(AppConstants.mediumPadding),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(AppConstants.mediumRadius),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceAround,
        children: [
          _buildStatItem('總計', stats['total']!, AppConstants.primaryColor),
          _buildStatItem('已學習', stats['learned']!, AppConstants.successColor),
          _buildStatItem('待學習', stats['remaining']!, AppConstants.warningColor),
        ],
      ),
    );
  }

  Widget _buildStatItem(String label, int value, Color color) {
    return Column(
      children: [
        Text(
          value.toString(),
          style: TextStyle(
            fontSize: 24,
            fontWeight: FontWeight.bold,
            color: color,
          ),
        ),
        Text(
          label,
          style: AppConstants.captionTextStyle,
        ),
      ],
    );
  }

  Widget _buildFlashcardList(List flashcards, String emptyMessage) {
    if (flashcards.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.inbox_outlined,
              size: 64,
              color: Colors.grey[400],
            ),
            const SizedBox(height: AppConstants.mediumPadding),
            Text(
              emptyMessage,
              style: AppConstants.bodyTextStyle.copyWith(
                color: Colors.grey[600],
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      );
    }

    return AnimationLimiter(
      child: ListView.builder(
        padding: const EdgeInsets.all(AppConstants.mediumPadding),
        itemCount: flashcards.length,
        itemBuilder: (context, index) {
          final flashcard = flashcards[index];
          return AnimationConfiguration.staggeredList(
            position: index,
            duration: AppConstants.mediumAnimationDuration,
            child: SlideAnimation(
              verticalOffset: 50.0,
              child: FadeInAnimation(
                child: Padding(
                  padding: const EdgeInsets.only(bottom: AppConstants.mediumPadding),
                  child: FlashcardWidget(
                    flashcard: flashcard,
                    onEdit: () => _navigateToEditScreen(context, flashcard),
                    onDelete: () => _showDeleteDialog(context, flashcard),
                    onToggleLearned: () => _toggleLearnedStatus(context, flashcard),
                  ),
                ),
              ),
            ),
          );
        },
      ),
    );
  }

  void _navigateToAddScreen(BuildContext context) {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => const AddEditFlashcardScreen(),
      ),
    );
  }

  void _navigateToEditScreen(BuildContext context, flashcard) {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => AddEditFlashcardScreen(flashcard: flashcard),
      ),
    );
  }

  void _navigateToStudyScreen(BuildContext context) {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => const StudyScreen(),
      ),
    );
  }

  void _toggleLearnedStatus(BuildContext context, flashcard) {
    final provider = context.read<FlashcardProvider>();
    if (flashcard.isLearned) {
      provider.markAsNotLearned(flashcard.id!);
    } else {
      provider.markAsLearned(flashcard.id!);
    }
  }

  void _showDeleteDialog(BuildContext context, flashcard) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('確認刪除'),
          content: const Text(AppConstants.deleteConfirmMessage),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('取消'),
            ),
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
                context.read<FlashcardProvider>().deleteFlashcard(flashcard.id!);
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(content: Text(AppConstants.deleteSuccessMessage)),
                );
              },
              child: const Text('刪除', style: TextStyle(color: Colors.red)),
            ),
          ],
        );
      },
    );
  }
}
