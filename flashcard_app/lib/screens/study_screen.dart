import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

import '../providers/flashcard_provider.dart';
import '../widgets/flashcard_widget.dart';
import '../widgets/custom_button.dart';
import '../utils/constants.dart';

/// 學習模式畫面
/// 
/// 提供專注的學習環境，讓使用者逐一學習未學習的單字卡
class StudyScreen extends StatefulWidget {
  const StudyScreen({super.key});

  @override
  State<StudyScreen> createState() => _StudyScreenState();
}

class _StudyScreenState extends State<StudyScreen> {
  int _currentIndex = 0;
  bool _isFlipped = false;
  PageController? _pageController;

  @override
  void initState() {
    super.initState();
    _pageController = PageController();
  }

  @override
  void dispose() {
    _pageController?.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text(
          '學習模式',
          style: AppConstants.titleTextStyle,
        ),
        backgroundColor: AppConstants.successColor,
        foregroundColor: Colors.white,
        elevation: 0,
        actions: [
          Consumer<FlashcardProvider>(
            builder: (context, provider, child) {
              final unlearnedCards = provider.unlearnedFlashcards;
              if (unlearnedCards.isEmpty) return const SizedBox.shrink();
              
              return Padding(
                padding: const EdgeInsets.only(right: AppConstants.mediumPadding),
                child: Center(
                  child: Text(
                    '${_currentIndex + 1} / ${unlearnedCards.length}',
                    style: const TextStyle(
                      color: Colors.white,
                      fontSize: 16,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
              );
            },
          ),
        ],
      ),
      body: Consumer<FlashcardProvider>(
        builder: (context, provider, child) {
          final unlearnedCards = provider.unlearnedFlashcards;

          if (unlearnedCards.isEmpty) {
            return Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(
                    Icons.celebration,
                    size: 80,
                    color: AppConstants.successColor,
                  ),
                  const SizedBox(height: AppConstants.mediumPadding),
                  Text(
                    AppConstants.noUnlearnedCardsMessage,
                    style: AppConstants.titleTextStyle.copyWith(
                      color: AppConstants.successColor,
                    ),
                    textAlign: TextAlign.center,
                  ),
                  const SizedBox(height: AppConstants.largePadding),
                  CustomButton(
                    text: '返回首頁',
                    onPressed: () => Navigator.of(context).pop(),
                    icon: Icons.home,
                  ),
                ],
              ),
            );
          }

          return Column(
            children: [
              // 進度條
              _buildProgressBar(unlearnedCards.length),
              
              // 卡片區域
              Expanded(
                child: PageView.builder(
                  controller: _pageController,
                  itemCount: unlearnedCards.length,
                  onPageChanged: (index) {
                    setState(() {
                      _currentIndex = index;
                      _isFlipped = false;
                    });
                  },
                  itemBuilder: (context, index) {
                    final flashcard = unlearnedCards[index];
                    return Padding(
                      padding: const EdgeInsets.all(AppConstants.mediumPadding),
                      child: Column(
                        children: [
                          // 學習提示
                          Container(
                            width: double.infinity,
                            padding: const EdgeInsets.all(AppConstants.mediumPadding),
                            decoration: BoxDecoration(
                              color: AppConstants.successColor.withValues(alpha: 0.1),
                              borderRadius: BorderRadius.circular(AppConstants.mediumRadius),
                            ),
                            child: Text(
                              _isFlipped
                                  ? '這是答案，你記住了嗎？'
                                  : '點擊卡片查看答案',
                              style: AppConstants.bodyTextStyle.copyWith(
                                color: AppConstants.successColor,
                                fontWeight: FontWeight.w500,
                              ),
                              textAlign: TextAlign.center,
                            ),
                          ),
                          
                          const SizedBox(height: AppConstants.mediumPadding),
                          
                          // 單字卡
                          Expanded(
                            child: FlashcardWidget(
                              flashcard: flashcard,
                              showActions: false,
                              isFlipped: _isFlipped,
                              onTap: () {
                                setState(() {
                                  _isFlipped = !_isFlipped;
                                });
                              },
                            ),
                          ),
                          
                          const SizedBox(height: AppConstants.mediumPadding),
                          
                          // 操作按鈕
                          if (_isFlipped) _buildActionButtons(flashcard, provider),
                        ],
                      ),
                    );
                  },
                ),
              ),
            ],
          );
        },
      ),
    );
  }

  Widget _buildProgressBar(int totalCards) {
    final progress = totalCards > 0 ? (_currentIndex + 1) / totalCards : 0.0;
    
    return Container(
      padding: const EdgeInsets.all(AppConstants.mediumPadding),
      child: Column(
        children: [
          LinearProgressIndicator(
            value: progress,
            backgroundColor: Colors.grey[300],
            valueColor: AlwaysStoppedAnimation<Color>(AppConstants.successColor),
            minHeight: 6,
          ),
          const SizedBox(height: AppConstants.smallPadding),
          Text(
            '學習進度：${_currentIndex + 1} / $totalCards',
            style: AppConstants.captionTextStyle,
          ),
        ],
      ),
    );
  }

  Widget _buildActionButtons(flashcard, FlashcardProvider provider) {
    return Row(
      children: [
        Expanded(
          child: CustomButton(
            text: '還不熟悉',
            onPressed: () => _nextCard(),
            backgroundColor: AppConstants.warningColor,
            icon: Icons.refresh,
          ),
        ),
        const SizedBox(width: AppConstants.mediumPadding),
        Expanded(
          child: CustomButton(
            text: '已學會',
            onPressed: () => _markAsLearned(flashcard, provider),
            backgroundColor: AppConstants.successColor,
            icon: Icons.check,
          ),
        ),
      ],
    );
  }

  void _nextCard() {
    final provider = context.read<FlashcardProvider>();
    final unlearnedCards = provider.unlearnedFlashcards;
    
    if (_currentIndex < unlearnedCards.length - 1) {
      _pageController?.nextPage(
        duration: AppConstants.mediumAnimationDuration,
        curve: Curves.easeInOut,
      );
    } else {
      // 已經是最後一張卡片
      _showCompletionDialog();
    }
  }

  void _markAsLearned(flashcard, FlashcardProvider provider) async {
    final success = await provider.markAsLearned(flashcard.id!);
    
    if (success) {
      // 顯示成功訊息
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('「${flashcard.front}」已標記為已學習'),
            backgroundColor: AppConstants.successColor,
            duration: const Duration(seconds: 1),
          ),
        );
      }
      
      // 檢查是否還有未學習的卡片
      final unlearnedCards = provider.unlearnedFlashcards;
      if (unlearnedCards.isEmpty) {
        _showCompletionDialog();
      } else {
        // 調整當前索引
        if (_currentIndex >= unlearnedCards.length) {
          setState(() {
            _currentIndex = unlearnedCards.length - 1;
          });
        }
        
        // 移動到下一張卡片或保持當前位置
        setState(() {
          _isFlipped = false;
        });
      }
    }
  }

  void _showCompletionDialog() {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return AlertDialog(
          title: Row(
            children: [
              Icon(
                Icons.celebration,
                color: AppConstants.successColor,
              ),
              const SizedBox(width: AppConstants.smallPadding),
              const Text('恭喜完成！'),
            ],
          ),
          content: const Text('你已經完成了所有未學習的單字卡！'),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.of(context).pop(); // 關閉對話框
                Navigator.of(context).pop(); // 返回首頁
              },
              child: const Text('返回首頁'),
            ),
          ],
        );
      },
    );
  }
}
