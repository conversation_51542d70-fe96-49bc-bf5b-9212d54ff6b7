import 'package:flutter/foundation.dart';
import '../models/flashcard.dart';
import '../services/flashcard_service.dart';

/// Flashcard 狀態管理 Provider
/// 
/// 使用 ChangeNotifier 管理單字卡的狀態，提供 UI 層所需的資料和操作方法
class FlashcardProvider extends ChangeNotifier {
  final FlashcardService _flashcardService = FlashcardService();

  // 私有狀態變數
  List<Flashcard> _flashcards = [];
  bool _isLoading = false;
  String? _errorMessage;
  Map<String, int> _studyStats = {
    'total': 0,
    'learned': 0,
    'remaining': 0,
  };

  // 公開的 getter
  List<Flashcard> get flashcards => List.unmodifiable(_flashcards);
  bool get isLoading => _isLoading;
  String? get errorMessage => _errorMessage;
  Map<String, int> get studyStats => Map.unmodifiable(_studyStats);

  // 篩選後的單字卡列表
  List<Flashcard> get learnedFlashcards =>
      _flashcards.where((card) => card.isLearned).toList();

  List<Flashcard> get unlearnedFlashcards =>
      _flashcards.where((card) => !card.isLearned).toList();

  /// 初始化載入所有單字卡
  Future<void> loadFlashcards() async {
    _setLoading(true);
    _clearError();

    try {
      _flashcards = await _flashcardService.getAllFlashcards();
      await _updateStudyStats();
    } catch (e) {
      _setError('載入單字卡失敗：${e.toString()}');
    } finally {
      _setLoading(false);
    }
  }

  /// 新增單字卡
  Future<bool> addFlashcard({
    required String front,
    required String back,
  }) async {
    _clearError();

    // 驗證輸入內容
    final validationError = _flashcardService.validateFlashcardContent(front, back);
    if (validationError != null) {
      _setError(validationError);
      return false;
    }

    try {
      final newFlashcard = await _flashcardService.createFlashcard(
        front: front,
        back: back,
      );

      _flashcards.insert(0, newFlashcard);
      await _updateStudyStats();
      notifyListeners();
      return true;
    } catch (e) {
      _setError('新增單字卡失敗：${e.toString()}');
      return false;
    }
  }

  /// 更新單字卡
  Future<bool> updateFlashcard({
    required int id,
    required String front,
    required String back,
  }) async {
    _clearError();

    // 驗證輸入內容
    final validationError = _flashcardService.validateFlashcardContent(front, back);
    if (validationError != null) {
      _setError(validationError);
      return false;
    }

    try {
      final updatedFlashcard = await _flashcardService.updateFlashcard(
        id: id,
        front: front,
        back: back,
      );

      final index = _flashcards.indexWhere((card) => card.id == id);
      if (index != -1) {
        _flashcards[index] = updatedFlashcard;
        notifyListeners();
      }
      return true;
    } catch (e) {
      _setError('更新單字卡失敗：${e.toString()}');
      return false;
    }
  }

  /// 標記單字卡為已學習
  Future<bool> markAsLearned(int id) async {
    _clearError();

    try {
      final updatedFlashcard = await _flashcardService.markAsLearned(id);
      final index = _flashcards.indexWhere((card) => card.id == id);
      if (index != -1) {
        _flashcards[index] = updatedFlashcard;
        await _updateStudyStats();
        notifyListeners();
      }
      return true;
    } catch (e) {
      _setError('更新學習狀態失敗：${e.toString()}');
      return false;
    }
  }

  /// 標記單字卡為未學習
  Future<bool> markAsNotLearned(int id) async {
    _clearError();

    try {
      final updatedFlashcard = await _flashcardService.markAsNotLearned(id);
      final index = _flashcards.indexWhere((card) => card.id == id);
      if (index != -1) {
        _flashcards[index] = updatedFlashcard;
        await _updateStudyStats();
        notifyListeners();
      }
      return true;
    } catch (e) {
      _setError('更新學習狀態失敗：${e.toString()}');
      return false;
    }
  }

  /// 刪除單字卡
  Future<bool> deleteFlashcard(int id) async {
    _clearError();

    try {
      await _flashcardService.deleteFlashcard(id);
      _flashcards.removeWhere((card) => card.id == id);
      await _updateStudyStats();
      notifyListeners();
      return true;
    } catch (e) {
      _setError('刪除單字卡失敗：${e.toString()}');
      return false;
    }
  }

  /// 清除錯誤訊息
  void clearError() {
    _clearError();
  }

  // 私有輔助方法
  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  void _setError(String error) {
    _errorMessage = error;
    notifyListeners();
  }

  void _clearError() {
    if (_errorMessage != null) {
      _errorMessage = null;
      notifyListeners();
    }
  }

  Future<void> _updateStudyStats() async {
    try {
      _studyStats = await _flashcardService.getStudyStats();
    } catch (e) {
      // 統計資料更新失敗不影響主要功能
      debugPrint('更新統計資料失敗：${e.toString()}');
    }
  }
}
