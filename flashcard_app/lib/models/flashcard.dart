/// Flashcard 資料模型
/// 
/// 代表一張單字卡，包含正面內容、背面內容、學習狀態等資訊
class Flashcard {
  final int? id;
  final String front;
  final String back;
  final bool isLearned;
  final DateTime createdAt;
  final DateTime updatedAt;

  const Flashcard({
    this.id,
    required this.front,
    required this.back,
    this.isLearned = false,
    required this.createdAt,
    required this.updatedAt,
  });

  /// 從資料庫 Map 建立 Flashcard 物件
  factory Flashcard.fromMap(Map<String, dynamic> map) {
    return Flashcard(
      id: map['id'] as int?,
      front: map['front'] as String,
      back: map['back'] as String,
      isLearned: (map['isLearned'] as int) == 1,
      createdAt: DateTime.parse(map['createdAt'] as String),
      updatedAt: DateTime.parse(map['updatedAt'] as String),
    );
  }

  /// 轉換為資料庫 Map 格式
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'front': front,
      'back': back,
      'isLearned': isLearned ? 1 : 0,
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt.toIso8601String(),
    };
  }

  /// 建立副本並更新指定欄位
  Flashcard copyWith({
    int? id,
    String? front,
    String? back,
    bool? isLearned,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return Flashcard(
      id: id ?? this.id,
      front: front ?? this.front,
      back: back ?? this.back,
      isLearned: isLearned ?? this.isLearned,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is Flashcard &&
        other.id == id &&
        other.front == front &&
        other.back == back &&
        other.isLearned == isLearned &&
        other.createdAt == createdAt &&
        other.updatedAt == updatedAt;
  }

  @override
  int get hashCode {
    return Object.hash(
      id,
      front,
      back,
      isLearned,
      createdAt,
      updatedAt,
    );
  }

  @override
  String toString() {
    return 'Flashcard(id: $id, front: $front, back: $back, isLearned: $isLearned, createdAt: $createdAt, updatedAt: $updatedAt)';
  }
}
