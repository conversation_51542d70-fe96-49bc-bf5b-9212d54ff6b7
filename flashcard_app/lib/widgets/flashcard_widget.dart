import 'package:flutter/material.dart';
import '../models/flashcard.dart';
import '../utils/constants.dart';

/// 單字卡顯示元件
/// 
/// 可翻轉的卡片元件，用於顯示單字卡的正面和背面內容
class FlashcardWidget extends StatefulWidget {
  final Flashcard flashcard;
  final VoidCallback? onTap;
  final VoidCallback? onEdit;
  final VoidCallback? onDelete;
  final VoidCallback? onToggleLearned;
  final bool showActions;
  final bool isFlipped;

  const FlashcardWidget({
    super.key,
    required this.flashcard,
    this.onTap,
    this.onEdit,
    this.onDelete,
    this.onToggleLearned,
    this.showActions = true,
    this.isFlipped = false,
  });

  @override
  State<FlashcardWidget> createState() => _FlashcardWidgetState();
}

class _FlashcardWidgetState extends State<FlashcardWidget>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _flipAnimation;
  bool _isFlipped = false;

  @override
  void initState() {
    super.initState();
    _isFlipped = widget.isFlipped;
    _animationController = AnimationController(
      duration: AppConstants.mediumAnimationDuration,
      vsync: this,
    );
    _flipAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));

    if (_isFlipped) {
      _animationController.value = 1.0;
    }
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  void _flipCard() {
    if (_isFlipped) {
      _animationController.reverse();
    } else {
      _animationController.forward();
    }
    setState(() {
      _isFlipped = !_isFlipped;
    });
    widget.onTap?.call();
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: _flipCard,
      child: AnimatedBuilder(
        animation: _flipAnimation,
        builder: (context, child) {
          final isShowingFront = _flipAnimation.value < 0.5;
          return Transform(
            alignment: Alignment.center,
            transform: Matrix4.identity()
              ..setEntry(3, 2, 0.001)
              ..rotateY(_flipAnimation.value * 3.14159),
            child: Card(
              elevation: 4,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(AppConstants.mediumRadius),
              ),
              child: Container(
                constraints: const BoxConstraints(
                  minHeight: AppConstants.cardMinHeight,
                  maxHeight: AppConstants.cardMaxHeight,
                ),
                padding: const EdgeInsets.all(AppConstants.mediumPadding),
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(AppConstants.mediumRadius),
                  gradient: LinearGradient(
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                    colors: widget.flashcard.isLearned
                        ? [
                            AppConstants.successColor.withValues(alpha: 0.1),
                            AppConstants.successColor.withValues(alpha: 0.05),
                          ]
                        : [
                            AppConstants.primaryColor.withValues(alpha: 0.1),
                            AppConstants.primaryColor.withValues(alpha: 0.05),
                          ],
                  ),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.stretch,
                  children: [
                    // 卡片內容
                    Expanded(
                      child: Center(
                        child: Transform(
                          alignment: Alignment.center,
                          transform: Matrix4.identity()
                            ..rotateY(isShowingFront ? 0 : 3.14159),
                          child: Text(
                            isShowingFront
                                ? widget.flashcard.front
                                : widget.flashcard.back,
                            style: AppConstants.bodyTextStyle.copyWith(
                              fontSize: 18,
                              fontWeight: FontWeight.w500,
                            ),
                            textAlign: TextAlign.center,
                          ),
                        ),
                      ),
                    ),
                    
                    // 狀態指示器和操作按鈕
                    if (widget.showActions) ...[
                      const SizedBox(height: AppConstants.smallPadding),
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          // 學習狀態指示器
                          Container(
                            padding: const EdgeInsets.symmetric(
                              horizontal: AppConstants.smallPadding,
                              vertical: 4,
                            ),
                            decoration: BoxDecoration(
                              color: widget.flashcard.isLearned
                                  ? AppConstants.successColor
                                  : AppConstants.warningColor,
                              borderRadius: BorderRadius.circular(12),
                            ),
                            child: Text(
                              widget.flashcard.isLearned ? '已學習' : '未學習',
                              style: const TextStyle(
                                color: Colors.white,
                                fontSize: 12,
                                fontWeight: FontWeight.w500,
                              ),
                            ),
                          ),
                          
                          // 操作按鈕
                          Row(
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              if (widget.onToggleLearned != null)
                                IconButton(
                                  icon: Icon(
                                    widget.flashcard.isLearned
                                        ? Icons.check_circle
                                        : Icons.check_circle_outline,
                                    color: widget.flashcard.isLearned
                                        ? AppConstants.successColor
                                        : Colors.grey,
                                  ),
                                  onPressed: widget.onToggleLearned,
                                  tooltip: widget.flashcard.isLearned
                                      ? '標記為未學習'
                                      : '標記為已學習',
                                ),
                              if (widget.onEdit != null)
                                IconButton(
                                  icon: const Icon(Icons.edit, color: Colors.blue),
                                  onPressed: widget.onEdit,
                                  tooltip: '編輯',
                                ),
                              if (widget.onDelete != null)
                                IconButton(
                                  icon: const Icon(Icons.delete, color: Colors.red),
                                  onPressed: widget.onDelete,
                                  tooltip: '刪除',
                                ),
                            ],
                          ),
                        ],
                      ),
                    ],
                  ],
                ),
              ),
            ),
          );
        },
      ),
    );
  }
}
