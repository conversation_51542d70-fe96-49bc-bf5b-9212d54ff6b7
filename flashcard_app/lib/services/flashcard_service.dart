import '../models/flashcard.dart';
import '../utils/database_helper.dart';

/// Flashcard 服務層
/// 
/// 提供單字卡相關的業務邏輯操作，作為資料存取層的封裝
class FlashcardService {
  final DatabaseHelper _databaseHelper = DatabaseHelper();

  /// 建立新的單字卡
  Future<Flashcard> createFlashcard({
    required String front,
    required String back,
  }) async {
    final now = DateTime.now();
    final flashcard = Flashcard(
      front: front.trim(),
      back: back.trim(),
      createdAt: now,
      updatedAt: now,
    );

    final id = await _databaseHelper.insertFlashcard(flashcard);
    return flashcard.copyWith(id: id);
  }

  /// 取得所有單字卡
  Future<List<Flashcard>> getAllFlashcards() async {
    return await _databaseHelper.getAllFlashcards();
  }

  /// 根據 ID 取得單字卡
  Future<Flashcard?> getFlashcard(int id) async {
    return await _databaseHelper.getFlashcard(id);
  }

  /// 更新單字卡內容
  Future<Flashcard> updateFlashcard({
    required int id,
    required String front,
    required String back,
    bool? isLearned,
  }) async {
    final existingFlashcard = await _databaseHelper.getFlashcard(id);
    if (existingFlashcard == null) {
      throw Exception('找不到指定的單字卡');
    }

    final updatedFlashcard = existingFlashcard.copyWith(
      front: front.trim(),
      back: back.trim(),
      isLearned: isLearned,
      updatedAt: DateTime.now(),
    );

    await _databaseHelper.updateFlashcard(updatedFlashcard);
    return updatedFlashcard;
  }

  /// 標記單字卡為已學習
  Future<Flashcard> markAsLearned(int id) async {
    final existingFlashcard = await _databaseHelper.getFlashcard(id);
    if (existingFlashcard == null) {
      throw Exception('找不到指定的單字卡');
    }

    final updatedFlashcard = existingFlashcard.copyWith(
      isLearned: true,
      updatedAt: DateTime.now(),
    );

    await _databaseHelper.updateFlashcard(updatedFlashcard);
    return updatedFlashcard;
  }

  /// 標記單字卡為未學習
  Future<Flashcard> markAsNotLearned(int id) async {
    final existingFlashcard = await _databaseHelper.getFlashcard(id);
    if (existingFlashcard == null) {
      throw Exception('找不到指定的單字卡');
    }

    final updatedFlashcard = existingFlashcard.copyWith(
      isLearned: false,
      updatedAt: DateTime.now(),
    );

    await _databaseHelper.updateFlashcard(updatedFlashcard);
    return updatedFlashcard;
  }

  /// 刪除單字卡
  Future<void> deleteFlashcard(int id) async {
    final result = await _databaseHelper.deleteFlashcard(id);
    if (result == 0) {
      throw Exception('找不到指定的單字卡');
    }
  }

  /// 取得學習統計資料
  Future<Map<String, int>> getStudyStats() async {
    return await _databaseHelper.getStudyStats();
  }

  /// 取得未學習的單字卡
  Future<List<Flashcard>> getUnlearnedFlashcards() async {
    final allFlashcards = await getAllFlashcards();
    return allFlashcards.where((flashcard) => !flashcard.isLearned).toList();
  }

  /// 取得已學習的單字卡
  Future<List<Flashcard>> getLearnedFlashcards() async {
    final allFlashcards = await getAllFlashcards();
    return allFlashcards.where((flashcard) => flashcard.isLearned).toList();
  }

  /// 驗證單字卡內容
  String? validateFlashcardContent(String front, String back) {
    if (front.trim().isEmpty) {
      return '正面內容不能為空';
    }
    if (back.trim().isEmpty) {
      return '背面內容不能為空';
    }
    if (front.trim().length > 500) {
      return '正面內容不能超過 500 個字元';
    }
    if (back.trim().length > 500) {
      return '背面內容不能超過 500 個字元';
    }
    return null;
  }
}
